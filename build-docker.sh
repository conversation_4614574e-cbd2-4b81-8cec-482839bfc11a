#!/bin/bash

module="engine"

if [ ! -z $2 ]; then
  module=$2
fi

if [ "$module" = "engine" ]; then
    # 如果 module 是 engine，暴露 10001 端口
    EXPOSE_PORT=10001
    echo "Module is engine, exposing port $EXPOSE_PORT"
elif [ "$module" = "dashboard" ]; then
    # 如果 module 是 dashboard，暴露 10002 端口
    EXPOSE_PORT=10002
    echo "Module is internal_api, exposing port $EXPOSE_PORT"
else
    # 如果 module 是其他值，可以设置默认端口或者报错
    echo "Unknown module: $module"
    exit 1
fi

# 镜像名称
IMAGE_NAME="audit_$module"

# 获取当前最新版本号
latest_version=$(docker images | grep $IMAGE_NAME | awk '{print $2}' | sort -nr | head -n 1)

# 检查是否获取到版本号
if [ -z "$latest_version" ]; then
    echo "No version found for image $IMAGE_NAME"
    latest_version=0
fi

# 增加版本号
new_version=$((latest_version + 1))
build_env="prod"

if [ ! -z $1 ]; then
  build_env=$1
fi

# 处理依赖
go mod vendor

# 构建新镜像
docker build --build-arg BUILD_ENV=$build_env --build-arg MODULE=$module -f Dockerfile -t $IMAGE_NAME:$new_version  .

# 输出新版本号
echo "New image version is $new_version"

# 停止旧版本的
docker ps |grep $IMAGE_NAME:$latest_version|awk '{print $1}' |xargs docker stop


if [ "$build_env" = "dev" ]; then
  docker run -d --name $IMAGE_NAME-$new_version -e AUDIT_CONFIG_CRYPT_KEY -p $EXPOSE_PORT:$EXPOSE_PORT -v /Users/<USER>/logs:/go/src/audit/output/logs $IMAGE_NAME:$new_version
elif [ "$build_env" = "test" ]; then
  docker run -d --name $IMAGE_NAME-$new_version -e AUDIT_CONFIG_CRYPT_KEY -p $EXPOSE_PORT:$EXPOSE_PORT -v /root/data/logs/audit:/go/src/audit/output/logs $IMAGE_NAME:$new_version
else
  docker run -d --name $IMAGE_NAME-$new_version -e AUDIT_CONFIG_CRYPT_KEY -p $EXPOSE_PORT:$EXPOSE_PORT -v /root/data/logs/audit:/go/src/audit/output/logs $IMAGE_NAME:$new_version
fi
