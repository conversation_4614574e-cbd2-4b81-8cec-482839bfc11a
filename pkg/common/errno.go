/**
 * @note
 * errno
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"gitlab.docsl.com/security/common"
)

// 将特定的errno注入ErrnoDesc
func init() {
	for k, v := range ErrnoDesc {
		common.ErrnoDesc[k] = v
	}
}

const (
	ErrTriggerWorkflow = 10000

	// System相关错误码
	ErrQuerySystemList   = 20001
	ErrQuerySystemDetail = 20002
	ErrSystemNotFound    = 20003
	ErrCreateSystem      = 20004
	ErrUpdateSystem      = 20005
	ErrDeleteSystem      = 20006

	ErrWebAuthnValidateBegin  = 100000
	ErrWebAuthnValidateFinish = 100001
	ErrWebAuthnSignupBegin    = 100002
	ErrWebAuthnSignupFinish   = 100003
)

var ErrnoDesc = map[int]string{
	ErrTriggerWorkflow: "触发流程失败",

	// System相关错误描述
	ErrQuerySystemList:   "查询系统列表失败",
	ErrQuerySystemDetail: "查询系统详情失败",
	ErrSystemNotFound:    "系统不存在",
	ErrCreateSystem:      "创建系统失败",
	ErrUpdateSystem:      "更新系统失败",
	ErrDeleteSystem:      "删除系统失败",

	ErrWebAuthnValidateBegin:  "Webauthn令牌校验初始化失败",
	ErrWebAuthnValidateFinish: "Webauthn令牌校验失败",
	ErrWebAuthnSignupBegin:    "Webauthn令牌注册初始化失败",
	ErrWebAuthnSignupFinish:   "Webauthn令牌注册失败",
}
