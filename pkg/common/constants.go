/**
 * @note
 * constants.go
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"os"
	"time"
)

var (
	Hostname string
)

const (
	ModuleName = "audit"

	DBName = "audit"
)

func init() {
	Hostname, _ = os.Hostname()
	Hostname = ModuleName + "_" + Hostname
}

var (
	LocalLocation, _ = time.LoadLocation("Asia/Shanghai")
)

// 参数定义，操作符等
const (
	UserRoleReadonly = "readonly"
	UserRoleAdmin    = "admin"
)

const (
	OperationWebAuthnRegister = "webAuthnRegister"
)

// table name
const (
	UserTableName             = "audit_user"
	OpLogTableName            = "audit_operation_log"
	AccessKeyTableName        = "audit_access_key"
	SystemTableName           = "audit_system"
	WorkflowTemplateTableName = "audit_workflow_template"
	NodeTemplateTableName     = "audit_node_template"
	WorkflowRuntimeTableName  = "audit_workflow_runtime"
	NodeRuntimeTableName      = "audit_node_runtime"

	UserCacheKeyFormat   = "audit_cache_user_%s"
	UserCacheExpirations = 120 * time.Second
)

const (
	KeyTypeED25519 = "ed25519"
)

type NodeState int64

type WorkflowState int64

// 节点状态常量
const (
	NodeStateInit   NodeState = 0 // 初始化
	NodeStatePass   NodeState = 1 // 通过
	NodeStateFailed NodeState = 2 // 校验失败
)

const (
	WorkflowStateInit       WorkflowState = 0 // 初始化
	WorkflowStateProcessing WorkflowState = 1 // 执行中
	WorkflowStatePass       WorkflowState = 2 // 通过
	WorkflowStateFailed     WorkflowState = 3 // 校验失败
)

var FlowStateNameMap = map[WorkflowState]string{
	WorkflowStateInit:       "init",
	WorkflowStateProcessing: "processing",
	WorkflowStatePass:       "pass",
	WorkflowStateFailed:     "failed",
}

var NodeStateNameMap = map[NodeState]string{
	NodeStateInit:   "init",
	NodeStatePass:   "pass",
	NodeStateFailed: "failed",
}

const (
	SignHeaderKey       = "X-Api-Key"
	SignHeaderSignature = "X-Api-Signature"
	SignHeaderTimestamp = "X-Api-Timestamp"
)

const (
	KeySystem = "system"
)

type TopologyType string

const (
	TopologyTypeAll      = "all"
	TopologyTypeSome     = "some"
	TopologyTypePrevious = "previous"
)

var (
	RoleToPathMap = map[string]map[string]bool{
		UserRoleReadonly: ReadonlyPath,
		UserRoleAdmin:    AdminPath,
	}

	ReadonlyPath = map[string]bool{}
	AdminPath    = map[string]bool{}
)
