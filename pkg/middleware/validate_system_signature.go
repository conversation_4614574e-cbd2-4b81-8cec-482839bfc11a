/**
 * @note
 * validate_system_signature
 *
 * <AUTHOR>
 * @date 	2025-06-30
 */
package middleware

import (
	"crypto/ed25519"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"

	systemModel "gitlab.docsl.com/security/audit/pkg/model/system"
	"gitlab.docsl.com/security/common"
)

// ValidateSystemSignature 验证系统签名
func ValidateSystemSignature() iris.Handler {
	return func(ctx iris.Context) {
		// 获取请求头中的签名信息
		apiKey := ctx.GetHeader(auditCommon.SignHeaderKey)
		signature := ctx.GetHeader(auditCommon.SignHeaderSignature)
		timestamp := ctx.GetHeader(auditCommon.SignHeaderTimestamp)

		if apiKey == common.StringEmpty || signature == common.StringEmpty || timestamp == common.StringEmpty {
			common.SetRet(ctx, common.NewError(common.ErrCodeInterSignature,
				fmt.Errorf("missing required headers: %s, %s, %s",
					auditCommon.SignHeaderKey, auditCommon.SignHeaderSignature, auditCommon.SignHeaderTimestamp)))
			return
		}

		// 验证时间戳（防重放攻击）
		if err := validateTimestamp(timestamp); err != nil {
			common.SetRet(ctx, common.NewError(common.ErrCodeInterSignature, err))
			return
		}

		// 根据 API Key 获取系统信息
		system, err := systemModel.GetSystemByPublicKey(ctx, apiKey)
		if err != nil {
			common.SetRet(ctx, common.NewError(common.ErrCodeInterSignature,
				fmt.Errorf("invalid api key: %s", apiKey)))
			return
		}

		// 读取请求体
		requestBody, err := ctx.GetBody()
		if err != nil {
			common.SetRet(ctx, common.NewError(common.ErrCodeInterSignature, err))
			return
		}

		// 验证签名
		if err := verifySignature(ctx, system.PublicKey, signature, timestamp, string(requestBody)); err != nil {
			common.SetRet(ctx, common.NewError(common.ErrCodeInterSignature, err))
			return
		}

		ctx.Values().Set(auditCommon.KeySystem, system.SystemID)
		// 验证通过，继续处理请求
		ctx.Next()
	}
}

// validateTimestamp 验证时间戳，防止重放攻击
func validateTimestamp(timestampStr string) error {
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid timestamp format: %s", timestampStr)
	}

	now := time.Now().UnixMilli()
	diff := now - timestamp

	// 允许5分钟的时间差
	const maxTimeDiff = 5 * 60 * 1000 // 5分钟，单位毫秒
	if diff > maxTimeDiff || diff < -maxTimeDiff {
		return fmt.Errorf("timestamp expired or invalid: %d", timestamp)
	}

	return nil
}

// getRequestBody 获取请求体内容
func getRequestBody(ctx iris.Context) (string, error) {
	// 记录请求体以便多次读取
	ctx.RecordRequestBody(true)

	body, err := io.ReadAll(ctx.Request().Body)
	if err != nil {
		return "", fmt.Errorf("failed to read request body: %v", err)
	}

	return string(body), nil
}

// verifySignature 验证签名
func verifySignature(ctx iris.Context, publicKeyHex, signatureHex, timestamp, requestBody string) error {
	// 解码公钥
	publicKeyBytes, err := hex.DecodeString(publicKeyHex)
	if err != nil {
		return fmt.Errorf("invalid public key format: %v", err)
	}

	if len(publicKeyBytes) != ed25519.PublicKeySize {
		return fmt.Errorf("invalid public key size: expected %d, got %d",
			ed25519.PublicKeySize, len(publicKeyBytes))
	}

	publicKey := ed25519.PublicKey(publicKeyBytes)

	// 解码签名
	signatureBytes, err := hex.DecodeString(signatureHex)
	if err != nil {
		return fmt.Errorf("invalid signature format: %v", err)
	}

	// 构造与签名时相同的消息
	method := strings.ToUpper(ctx.Method())
	path := ctx.Request().URL.Path
	query := ctx.Request().URL.RawQuery

	message := fmt.Sprintf("%s|%s|%s|%s|%s", method, path, timestamp, query, requestBody)

	// 计算两次 SHA256 哈希（与签名生成时保持一致）
	hash1 := sha256.Sum256([]byte(message))
	hash2 := sha256.Sum256(hash1[:])
	doubleHash := hash2[:]

	// 验证签名
	if !ed25519.Verify(publicKey, doubleHash, signatureBytes) {
		return fmt.Errorf("signature verification failed")
	}

	return nil
}
