/**
 * @note
 * workflow model
 *
 * <AUTHOR>
 * @date 	2025-06-26
 */
package flow

import (
	"context"
	"fmt"
	"sort"
	"time"

	"gorm.io/gorm"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
)

type WorkflowModel interface {
	// WorkflowTemplate 相关方法
	GetWorkflowTemplateByID(ctx context.Context, id uint) (*AuditWorkflowTemplateTable, error)
	GetLatestWorkflowTemplateByName(ctx context.Context, workflowName string) (*AuditWorkflowTemplateTable, error)
	QueryWorkflowTemplatesBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) ([]*AuditWorkflowTemplateTable, error)
	QueryWorkflowTemplatesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) (int64, error)
	CreateWorkflowTemplate(ctx context.Context, workflowName, description string, nodeCount int64, isLatest bool) (id uint, err error)
	UpdateWorkflowTemplate(ctx context.Context, id uint, description *string, nodeCount *int64, isLatest *bool) error
	DeleteWorkflowTemplate(ctx context.Context, id uint) error

	// NodeTemplate 相关方法
	GetNodeTemplateByID(ctx context.Context, id uint) (*AuditNodeTemplateTable, error)
	QueryNodeTemplatesBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) ([]*AuditNodeTemplateTable, error)
	QueryNodeTemplatesCountBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) (int64, error)
	CreateNodeTemplate(ctx context.Context, nodeName string, workflowTemplateID int64, description string, nodeIndex int64, systemID string, config *NodeConfig) (id uint, err error)
	UpdateNodeTemplate(ctx context.Context, id uint, description *string, systemID *string, config *NodeConfig) error
	DeleteNodeTemplate(ctx context.Context, id uint) error

	// WorkflowRuntime 相关方法
	GetWorkflowRuntimeByID(ctx context.Context, id uint) (*AuditWorkflowRuntimeTable, error)
	QueryWorkflowRuntimesBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) ([]*AuditWorkflowRuntimeTable, error)
	QueryWorkflowRuntimesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) (int64, error)
	CreateWorkflowRuntimeWithTx(ctx context.Context, tx *gorm.DB, workflowName string, workflowTemplateID int64, nodeCount int64, state auditCommon.WorkflowState) (id uint, err error)
	UpdateWorkflowRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, nodeCount *int64, state *auditCommon.WorkflowState) error
	DeleteWorkflowRuntime(ctx context.Context, id uint) error

	// NodeRuntime 相关方法
	GetNodeRuntimeByID(ctx context.Context, id uint) (*AuditNodeRuntimeTable, error)
	QueryNodeRuntimesBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) ([]*AuditNodeRuntimeTable, error)
	QueryNodeRuntimesCountBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) (int64, error)
	CreateNodeRuntimeWithTx(ctx context.Context, tx *gorm.DB, workflowInstanceID int64, nodeName string, nodeTemplateID int64, systemID string, nodeIndex int64, data *NodeData, state auditCommon.NodeState) (id uint, err error)
	UpdateNodeRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, data *NodeData, state *auditCommon.NodeState) error
	DeleteNodeRuntime(ctx context.Context, id uint) error
}

var DefaultService WorkflowModel = &WorkflowModelImpl{}

type WorkflowModelImpl struct{}

func (m *WorkflowModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(auditCommon.DBName, false, common.GetLogger(ctx))
}

// WorkflowTemplate 相关实现

func (m *WorkflowModelImpl) GetWorkflowTemplateByID(ctx context.Context, id uint) (*AuditWorkflowTemplateTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})
	ret := &AuditWorkflowTemplateTable{}
	db = db.Where("id = ?", id).First(ret)
	return ret, db.Error
}

func (m *WorkflowModelImpl) GetLatestWorkflowTemplateByName(ctx context.Context, workflowName string) (*AuditWorkflowTemplateTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})
	ret := &AuditWorkflowTemplateTable{}
	db = db.Where("workflow_name = ? AND is_latest = ?", workflowName, true).First(ret)
	return ret, db.Error
}

func assembleWorkflowTemplateQueryConditions(ctx context.Context, filter QueryWorkflowTemplateFilter, db *gorm.DB) *gorm.DB {
	if filter.WorkflowName != "" {
		db = db.Where("workflow_name = ?", filter.WorkflowName)
	}
	if filter.IsLatest != nil {
		db = db.Where("is_latest = ?", *filter.IsLatest)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}

	return db
}

func (m *WorkflowModelImpl) QueryWorkflowTemplatesBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) ([]*AuditWorkflowTemplateTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})

	db = assembleWorkflowTemplateQueryConditions(ctx, filter, db)

	// 分页
	if filter.Page > 0 && filter.PerPage > 0 {
		offset := (filter.Page - 1) * filter.PerPage
		db = db.Offset(offset).Limit(filter.PerPage)
	}

	var results []*AuditWorkflowTemplateTable
	db = db.Find(&results)
	return results, db.Error
}

func (m *WorkflowModelImpl) QueryWorkflowTemplatesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})

	db = assembleWorkflowTemplateQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *WorkflowModelImpl) CreateWorkflowTemplate(ctx context.Context, workflowName, description string, nodeCount int64, isLatest bool) (id uint, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}

	generatedID := idgen.GetID()
	tb := &AuditWorkflowTemplateTable{
		WorkflowName: workflowName,
		Description:  description,
		NodeCount:    nodeCount,
		IsLatest:     isLatest,
	}
	tb.ID = uint(generatedID)

	db = db.Create(tb)
	return tb.ID, db.Error
}

func (m *WorkflowModelImpl) UpdateWorkflowTemplate(ctx context.Context, id uint, description *string, nodeCount *int64, isLatest *bool) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})

	updateMap := make(map[string]interface{})
	if description != nil {
		updateMap["description"] = *description
	}
	if nodeCount != nil {
		updateMap["node_count"] = *nodeCount
	}
	if isLatest != nil {
		updateMap["is_latest"] = *isLatest
	}

	if len(updateMap) == 0 {
		return nil
	}

	db = db.Where("id = ?", id).Updates(updateMap)
	return db.Error
}

func (m *WorkflowModelImpl) DeleteWorkflowTemplate(ctx context.Context, id uint) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditWorkflowTemplateTable{})
	db = db.Where("id = ?", id).Delete(&AuditWorkflowTemplateTable{})
	return db.Error
}

// NodeTemplate 相关实现

func (m *WorkflowModelImpl) GetNodeTemplateByID(ctx context.Context, id uint) (*AuditNodeTemplateTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditNodeTemplateTable{})
	ret := &AuditNodeTemplateTable{}
	db = db.Where("id = ?", id).First(ret)
	return ret, db.Error
}

func assembleNodeTemplateQueryConditions(ctx context.Context, filter QueryNodeTemplateFilter, db *gorm.DB) *gorm.DB {
	if filter.WorkflowTemplateID != nil {
		db = db.Where("workflow_template_id = ?", *filter.WorkflowTemplateID)
	}
	if filter.NodeName != "" {
		db = db.Where("node_name = ?", filter.NodeName)
	}
	if filter.SystemID != "" {
		db = db.Where("system_id = ?", filter.SystemID)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}

	return db
}

func (m *WorkflowModelImpl) QueryNodeTemplatesBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) ([]*AuditNodeTemplateTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditNodeTemplateTable{})

	db = assembleNodeTemplateQueryConditions(ctx, filter, db)

	// 分页
	if filter.Page > 0 && filter.PerPage > 0 {
		offset := (filter.Page - 1) * filter.PerPage
		db = db.Offset(offset).Limit(filter.PerPage)
	}

	var results []*AuditNodeTemplateTable
	db = db.Find(&results)
	return results, db.Error
}

func (m *WorkflowModelImpl) QueryNodeTemplatesCountBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AuditNodeTemplateTable{})

	db = assembleNodeTemplateQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *WorkflowModelImpl) CreateNodeTemplate(ctx context.Context, nodeName string, workflowTemplateID int64, description string, nodeIndex int64, systemID string, config *NodeConfig) (id uint, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}

	generatedID := idgen.GetID()
	tb := &AuditNodeTemplateTable{
		NodeName:           nodeName,
		WorkflowTemplateID: workflowTemplateID,
		Description:        description,
		NodeIndex:          nodeIndex,
		SystemID:           systemID,
		Config:             config,
	}
	tb.ID = uint(generatedID)

	db = db.Create(tb)
	return tb.ID, db.Error
}

func (m *WorkflowModelImpl) UpdateNodeTemplate(ctx context.Context, id uint, description *string, systemID *string, config *NodeConfig) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditNodeTemplateTable{})

	updateMap := make(map[string]interface{})
	if description != nil {
		updateMap["description"] = *description
	}
	if systemID != nil {
		updateMap["system_id"] = *systemID
	}
	if config != nil {
		updateMap["config"] = config
	}

	if len(updateMap) == 0 {
		return nil
	}

	db = db.Where("id = ?", id).Updates(updateMap)
	return db.Error
}

func (m *WorkflowModelImpl) DeleteNodeTemplate(ctx context.Context, id uint) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditNodeTemplateTable{})
	db = db.Where("id = ?", id).Delete(&AuditNodeTemplateTable{})
	return db.Error
}

// WorkflowRuntime 相关实现

func (m *WorkflowModelImpl) GetWorkflowRuntimeByID(ctx context.Context, id uint) (*AuditWorkflowRuntimeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditWorkflowRuntimeTable{})
	ret := &AuditWorkflowRuntimeTable{}
	db = db.Where("id = ?", id).First(ret)
	return ret, db.Error
}

func assembleWorkflowRuntimeQueryConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter, db *gorm.DB) *gorm.DB {
	if filter.WorkflowName != "" {
		db = db.Where("workflow_name LIKE ?", "%"+filter.WorkflowName+"%")
	}
	if filter.WorkflowTemplateID != nil {
		db = db.Where("workflow_template_id = ?", *filter.WorkflowTemplateID)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}

	return db
}

func (m *WorkflowModelImpl) QueryWorkflowRuntimesBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) ([]*AuditWorkflowRuntimeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditWorkflowRuntimeTable{})

	db = assembleWorkflowRuntimeQueryConditions(ctx, filter, db)

	// 分页
	if filter.Page > 0 && filter.PerPage > 0 {
		offset := (filter.Page - 1) * filter.PerPage
		db = db.Offset(offset).Limit(filter.PerPage)
	}

	var results []*AuditWorkflowRuntimeTable
	db = db.Find(&results)
	return results, db.Error
}

func (m *WorkflowModelImpl) QueryWorkflowRuntimesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AuditWorkflowRuntimeTable{})

	db = assembleWorkflowRuntimeQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *WorkflowModelImpl) DeleteWorkflowRuntime(ctx context.Context, id uint) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditWorkflowRuntimeTable{})
	db = db.Where("id = ?", id).Delete(&AuditWorkflowRuntimeTable{})
	return db.Error
}

// NodeRuntime 相关实现

func (m *WorkflowModelImpl) GetNodeRuntimeByID(ctx context.Context, id uint) (*AuditNodeRuntimeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditNodeRuntimeTable{})
	ret := &AuditNodeRuntimeTable{}
	db = db.Where("id = ?", id).First(ret)
	return ret, db.Error
}

func assembleNodeRuntimeQueryConditions(ctx context.Context, filter QueryNodeRuntimeFilter, db *gorm.DB) *gorm.DB {
	if filter.WorkflowInstanceID != nil {
		db = db.Where("workflow_instance_id = ?", *filter.WorkflowInstanceID)
	}
	if filter.NodeName != "" {
		db = db.Where("node_name LIKE ?", "%"+filter.NodeName+"%")
	}
	if filter.SystemID != "" {
		db = db.Where("system_id = ?", filter.SystemID)
	}
	if filter.State != nil {
		db = db.Where("state = ?", *filter.State)
	}
	if filter.StartTime != nil {
		db = db.Where("created_at >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("created_at <= ?", *filter.EndTime)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}

	return db
}

func (m *WorkflowModelImpl) QueryNodeRuntimesBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) ([]*AuditNodeRuntimeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditNodeRuntimeTable{})

	db = assembleNodeRuntimeQueryConditions(ctx, filter, db)

	// 分页
	if filter.Page > 0 && filter.PerPage > 0 {
		offset := (filter.Page - 1) * filter.PerPage
		db = db.Offset(offset).Limit(filter.PerPage)
	}

	var results []*AuditNodeRuntimeTable
	db = db.Find(&results)
	return results, db.Error
}

func (m *WorkflowModelImpl) QueryNodeRuntimesCountBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AuditNodeRuntimeTable{})

	db = assembleNodeRuntimeQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *WorkflowModelImpl) DeleteNodeRuntime(ctx context.Context, id uint) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditNodeRuntimeTable{})
	db = db.Where("id = ?", id).Delete(&AuditNodeRuntimeTable{})
	return db.Error
}

// 导出函数，方便外部调用

// GetWorkflowTemplateByID 导出函数
func GetWorkflowTemplateByID(ctx context.Context, id uint) (*AuditWorkflowTemplateTable, error) {
	return DefaultService.GetWorkflowTemplateByID(ctx, id)
}

func GetLatestWorkflowAndNodeTmplByFlowName(ctx context.Context, flowName string) (*AuditWorkflowTemplateTable, []*AuditNodeTemplateTable, error) {
	if item := GetLatestWorkflowAndNodeTmplByNameFromCache(flowName); item != nil {
		return item.WorkflowTemplate, item.NodeTemplates, nil
	}
	workflowTemplate, err := DefaultService.GetLatestWorkflowTemplateByName(ctx, flowName)
	if err != nil {
		return nil, nil, err
	}
	templateID := int64(workflowTemplate.ID)
	nodeTemplateFilter := QueryNodeTemplateFilter{
		WorkflowTemplateID: &templateID,
	}
	nodeTemplates, err := DefaultService.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
	if err != nil {
		return nil, nil, err
	}
	if len(nodeTemplates) == 0 {
		return nil, nil, fmt.Errorf("no node templates found for workflow '%s'", flowName)
	}
	// 按 nodeIndex 排序
	sort.Slice(nodeTemplates, func(i, j int) bool {
		return nodeTemplates[i].NodeIndex < nodeTemplates[j].NodeIndex
	})
	for _, nodeTemplate := range nodeTemplates {
		if err = nodeTemplate.Config.CompileRules(); err != nil { // 查询时就提前编译好，这样后续可以一直使用
			return nil, nil, fmt.Errorf("node template '%s' compile rules failed: %v", nodeTemplate.NodeName, err)
		}
	}
	// 加入缓存
	SetLatestWorkflowAndNodeTmplByNameToCache(workflowTemplate, nodeTemplates)
	return workflowTemplate, nodeTemplates, nil
}

func GetWorkflowAndNodeTmplByFlowTemplateID(ctx context.Context, templateID int64) (*AuditWorkflowTemplateTable, []*AuditNodeTemplateTable, error) {
	if item := GetWorkflowAndNodeTmplByIDFromCache(templateID); item != nil {
		return item.WorkflowTemplate, item.NodeTemplates, nil
	}
	workflowTemplate, err := DefaultService.GetWorkflowTemplateByID(ctx, uint(templateID))
	if err != nil {
		return nil, nil, err
	}
	nodeTemplateFilter := QueryNodeTemplateFilter{
		WorkflowTemplateID: &templateID,
	}
	nodeTemplates, err := DefaultService.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
	if err != nil {
		return nil, nil, err
	}
	if len(nodeTemplates) == 0 {
		return nil, nil, fmt.Errorf("no node templates found for workflow template ID '%s'", templateID)
	}
	// 按 nodeIndex 排序
	sort.Slice(nodeTemplates, func(i, j int) bool {
		return nodeTemplates[i].NodeIndex < nodeTemplates[j].NodeIndex
	})
	for _, nodeTemplate := range nodeTemplates {
		if err = nodeTemplate.Config.CompileRules(); err != nil { // 查询时就提前编译好，这样后续可以一直使用
			return nil, nil, fmt.Errorf("node template '%s' compile rules failed: %v", nodeTemplate.NodeName, err)
		}
	}
	// 加入缓存
	SetWorkflowAndNodeTmplByIDToCache(workflowTemplate, nodeTemplates)
	return workflowTemplate, nodeTemplates, nil
}

func QueryWorkflowTemplatesBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) ([]*AuditWorkflowTemplateTable, error) {
	return DefaultService.QueryWorkflowTemplatesBySeveralConditions(ctx, filter)
}

func QueryWorkflowTemplatesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowTemplateFilter) (int64, error) {
	return DefaultService.QueryWorkflowTemplatesCountBySeveralConditions(ctx, filter)
}

func CreateWorkflowTemplate(ctx context.Context, workflowName, description string, nodeCount int64, isLatest bool) (uint, error) {
	return DefaultService.CreateWorkflowTemplate(ctx, workflowName, description, nodeCount, isLatest)
}

func UpdateWorkflowTemplate(ctx context.Context, id uint, description *string, nodeCount *int64, isLatest *bool) error {
	return DefaultService.UpdateWorkflowTemplate(ctx, id, description, nodeCount, isLatest)
}

func DeleteWorkflowTemplate(ctx context.Context, id uint) error {
	return DefaultService.DeleteWorkflowTemplate(ctx, id)
}

func GetNodeTemplateByID(ctx context.Context, id uint) (*AuditNodeTemplateTable, error) {
	return DefaultService.GetNodeTemplateByID(ctx, id)
}

func QueryNodeTemplatesBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) ([]*AuditNodeTemplateTable, error) {
	return DefaultService.QueryNodeTemplatesBySeveralConditions(ctx, filter)
}

func QueryNodeTemplatesCountBySeveralConditions(ctx context.Context, filter QueryNodeTemplateFilter) (int64, error) {
	return DefaultService.QueryNodeTemplatesCountBySeveralConditions(ctx, filter)
}

func CreateNodeTemplate(ctx context.Context, nodeName string, workflowTemplateID int64, description string, nodeIndex int64, systemID string, config *NodeConfig) (uint, error) {
	return DefaultService.CreateNodeTemplate(ctx, nodeName, workflowTemplateID, description, nodeIndex, systemID, config)
}

func UpdateNodeTemplate(ctx context.Context, id uint, description *string, systemID *string, config *NodeConfig) error {
	return DefaultService.UpdateNodeTemplate(ctx, id, description, systemID, config)
}

func DeleteNodeTemplate(ctx context.Context, id uint) error {
	return DefaultService.DeleteNodeTemplate(ctx, id)
}

// WorkflowRuntime 导出函数
func GetWorkflowRuntimeByID(ctx context.Context, id uint) (*AuditWorkflowRuntimeTable, error) {
	return DefaultService.GetWorkflowRuntimeByID(ctx, id)
}

func QueryWorkflowRuntimesBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) ([]*AuditWorkflowRuntimeTable, error) {
	return DefaultService.QueryWorkflowRuntimesBySeveralConditions(ctx, filter)
}

func QueryWorkflowRuntimesCountBySeveralConditions(ctx context.Context, filter QueryWorkflowRuntimeFilter) (int64, error) {
	return DefaultService.QueryWorkflowRuntimesCountBySeveralConditions(ctx, filter)
}

func UpdateWorkflowRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, nodeCount *int64, state *auditCommon.WorkflowState) error {
	return DefaultService.UpdateWorkflowRuntimeWithTx(ctx, tx, id, version, nodeCount, state)
}

func DeleteWorkflowRuntime(ctx context.Context, id uint) error {
	return DefaultService.DeleteWorkflowRuntime(ctx, id)
}

// NodeRuntime 导出函数
func GetNodeRuntimeByID(ctx context.Context, id uint) (*AuditNodeRuntimeTable, error) {
	return DefaultService.GetNodeRuntimeByID(ctx, id)
}

func QueryNodeRuntimesBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) ([]*AuditNodeRuntimeTable, error) {
	return DefaultService.QueryNodeRuntimesBySeveralConditions(ctx, filter)
}

func QueryNodeRuntimesCountBySeveralConditions(ctx context.Context, filter QueryNodeRuntimeFilter) (int64, error) {
	return DefaultService.QueryNodeRuntimesCountBySeveralConditions(ctx, filter)
}

func UpdateNodeRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, data *NodeData, state *auditCommon.NodeState) error {
	return DefaultService.UpdateNodeRuntimeWithTx(ctx, tx, id, version, data, state)
}

func DeleteNodeRuntime(ctx context.Context, id uint) error {
	return DefaultService.DeleteNodeRuntime(ctx, id)
}

// 事务支持的方法

// CreateWorkflowRuntimeWithTx 在指定事务中创建WorkflowRuntime
func (m *WorkflowModelImpl) CreateWorkflowRuntimeWithTx(ctx context.Context, tx *gorm.DB, workflowName string, workflowTemplateID int64, nodeCount int64, state auditCommon.WorkflowState) (id uint, err error) {
	generatedID := idgen.GetID()
	currentTime := time.Now().UnixMicro()
	tb := &AuditWorkflowRuntimeTable{
		WorkflowName:       workflowName,
		WorkflowTemplateID: workflowTemplateID,
		NodeCount:          nodeCount,
		Version:            currentTime,
		State:              state,
	}
	tb.ID = uint(generatedID)

	err = tx.Create(tb).Error
	return tb.ID, err
}

// CreateNodeRuntimeWithTx 在指定事务中创建NodeRuntime
func (m *WorkflowModelImpl) CreateNodeRuntimeWithTx(ctx context.Context, tx *gorm.DB, workflowInstanceID int64, nodeName string, nodeTemplateID int64, systemID string, nodeIndex int64, data *NodeData, state auditCommon.NodeState) (id uint, err error) {
	generatedID := idgen.GetID()
	currentTime := time.Now().UnixMicro()
	tb := &AuditNodeRuntimeTable{
		WorkflowInstanceID: workflowInstanceID,
		NodeName:           nodeName,
		NodeTemplateID:     nodeTemplateID,
		SystemID:           systemID,
		NodeIndex:          nodeIndex,
		Version:            currentTime,
		Data:               data,
		State:              state,
	}
	tb.ID = uint(generatedID)

	err = tx.Create(tb).Error
	return tb.ID, err
}

// UpdateWorkflowRuntimeWithTx 在指定事务中更新WorkflowRuntime
func (m *WorkflowModelImpl) UpdateWorkflowRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, nodeCount *int64, state *auditCommon.WorkflowState) error {
	updateMap := make(map[string]interface{})
	if nodeCount != nil {
		updateMap["node_count"] = *nodeCount
	}
	if state != nil {
		updateMap["state"] = *state
	}

	// 总是更新版本号为当前时间戳
	currentTime := time.Now().UnixMicro()
	updateMap["version"] = currentTime

	if len(updateMap) == 1 { // 只有version字段，说明没有实际更新内容
		return nil
	}

	// 乐观锁：只有当version小于当前时间戳时才更新
	result := tx.Model(&AuditWorkflowRuntimeTable{}).Where("id = ? AND version = ?", id, version).Updates(updateMap)
	if result.Error != nil {
		return result.Error
	}

	// 检查是否有行被更新，如果没有则说明乐观锁失败
	if result.RowsAffected == 0 {
		return auditCommon.ErrOptimisticLockFailed
	}

	return nil
}

func (m *WorkflowModelImpl) UpdateNodeRuntimeWithTx(ctx context.Context, tx *gorm.DB, id uint, version int64, data *NodeData, state *auditCommon.NodeState) error {
	tx = tx.Model(&AuditNodeRuntimeTable{})

	updateMap := make(map[string]interface{})
	if data != nil {
		updateMap["data"] = data
	}
	if state != nil {
		updateMap["state"] = *state
	}

	// 总是更新版本号为当前时间戳
	currentTime := time.Now().UnixMicro()
	updateMap["version"] = currentTime

	if len(updateMap) == 1 { // 只有version字段，说明没有实际更新内容
		return nil
	}

	// 乐观锁：只有当version小于当前时间戳时才更新
	result := tx.Where("id = ? AND version = ?", id, version).Updates(updateMap)
	if result.Error != nil {
		return result.Error
	}

	// 检查是否有行被更新，如果没有则说明乐观锁失败
	if result.RowsAffected == 0 {
		return auditCommon.ErrOptimisticLockFailed
	}

	return nil
}

// ExecuteInTransaction 执行事务
func ExecuteInTransaction(ctx context.Context, fn func(tx *gorm.DB) error) error {
	db, err := mysql.GetDB(auditCommon.DBName, false, common.GetLogger(ctx))
	if err != nil {
		return err
	}

	return db.Transaction(fn)
}

// createWorkflowRuntimeInTx 在事务中创建workflowRuntime
func CreateWorkflowRuntimeInTx(ctx context.Context, tx *gorm.DB,
	workflowTemplate *AuditWorkflowTemplateTable, workflowState auditCommon.WorkflowState) (uint, error) {

	// 使用事务中的数据库连接创建workflowRuntime
	return DefaultService.CreateWorkflowRuntimeWithTx(ctx, tx,
		workflowTemplate.WorkflowName,
		int64(workflowTemplate.ID),
		workflowTemplate.NodeCount,
		workflowState)
}

// createNodeRuntimeInTx 在事务中创建nodeRuntime
func CreateNodeRuntimeInTx(ctx context.Context, tx *gorm.DB,
	workflowInstanceID int64,
	nodeTemplate *AuditNodeTemplateTable,
	nodeData *NodeData,
	nodeState auditCommon.NodeState) (uint, error) {

	// 使用事务中的数据库连接创建nodeRuntime
	return DefaultService.CreateNodeRuntimeWithTx(ctx, tx,
		workflowInstanceID,
		nodeTemplate.NodeName,
		int64(nodeTemplate.ID),
		nodeTemplate.SystemID,
		nodeTemplate.NodeIndex,
		nodeData,
		nodeState)
}
