/**
 * @note
 * template_cache
 *
 * <AUTHOR>
 * @date 	2025-07-02
 */
package flow

import (
	"sync"
	"time"
)

const (
	WorkflowTemplateCacheByNameExpireTime = 10 * time.Minute
	WorkflowTemplateCacheByIDExpireTime   = 24 * time.Hour
)

type WorkflowTemplateCacheItem struct {
	WorkflowTemplate *AuditWorkflowTemplateTable
	NodeTemplates    []*AuditNodeTemplateTable
	ExpireTime       time.Time
}

var (
	workflowTemplateCache = sync.Map{}
)

func GetLatestWorkflowAndNodeTmplByNameFromCache(workflowName string) *WorkflowTemplateCacheItem {
	val, ok := workflowTemplateCache.Load(workflowName)
	if !ok {
		return nil
	} else if val.(*WorkflowTemplateCacheItem).ExpireTime.Before(time.Now()) {
		workflowTemplateCache.Delete(workflowName) // 过期了
		return nil
	} else {
		return val.(*WorkflowTemplateCacheItem)
	}
}

func SetLatestWorkflowAndNodeTmplByNameToCache(workflowTemplate *AuditWorkflowTemplateTable, nodeTemplates []*AuditNodeTemplateTable) {
	workflowTemplateCache.Store(workflowTemplate.WorkflowName, &WorkflowTemplateCacheItem{
		WorkflowTemplate: workflowTemplate,
		NodeTemplates:    nodeTemplates,
		ExpireTime:       time.Now().Add(WorkflowTemplateCacheByNameExpireTime),
	})
}

func GetWorkflowAndNodeTmplByIDFromCache(workflowTemplateID int64) *WorkflowTemplateCacheItem {
	val, ok := workflowTemplateCache.Load(workflowTemplateID)
	if !ok {
		return nil
	} else if val.(*WorkflowTemplateCacheItem).ExpireTime.Before(time.Now()) {
		workflowTemplateCache.Delete(workflowTemplateID) // 过期了
		return nil
	} else {
		return val.(*WorkflowTemplateCacheItem)
	}
}

func SetWorkflowAndNodeTmplByIDToCache(workflowTemplate *AuditWorkflowTemplateTable, nodeTemplates []*AuditNodeTemplateTable) {
	workflowTemplateCache.Store(int64(workflowTemplate.ID), &WorkflowTemplateCacheItem{
		WorkflowTemplate: workflowTemplate,
		NodeTemplates:    nodeTemplates,
		ExpireTime:       time.Now().Add(WorkflowTemplateCacheByIDExpireTime),
	})
}
