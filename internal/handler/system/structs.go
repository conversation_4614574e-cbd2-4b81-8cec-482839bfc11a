/**
 * @note
 * system structs
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"time"
	
	systemModel "gitlab.docsl.com/security/audit/pkg/model/system"
)

// ListSystemRequest 查询系统列表请求
type ListSystemRequest struct {
	Page       int    `json:"page" validate:"min=1"`
	PerPage    int    `json:"perPage" validate:"min=1,max=100"`
	SystemID   string `json:"systemID"`
	SystemName string `json:"systemName"`
	KeyType    string `json:"keyType"`
	Order      string `json:"order"`
}

// ListSystemResponse 查询系统列表响应
type ListSystemResponse struct {
	Total int64             `json:"total"`
	List  []*SystemResponse `json:"list"`
}

// CreateSystemRequest 创建系统请求
type CreateSystemRequest struct {
	SystemID    string `json:"systemID" validate:"required"`
	SystemName  string `json:"systemName" validate:"required"`
	Description string `json:"description"`
	KeyType     string `json:"keyType" validate:"required"`
	PublicKey   string `json:"publicKey" validate:"required"`
}

// CreateSystemResponse 创建系统响应
type CreateSystemResponse struct {
	ID uint `json:"id"`
}

// UpdateSystemRequest 更新系统请求
type UpdateSystemRequest struct {
	ID          uint    `json:"id" validate:"required"`
	SystemName  *string `json:"systemName"`
	Description *string `json:"description"`
	KeyType     *string `json:"keyType"`
	PublicKey   *string `json:"publicKey"`
}

// DeleteSystemRequest 删除系统请求
type DeleteSystemRequest struct {
	ID uint `json:"id" validate:"required"`
}

// GetSystemRequest 获取系统详情请求
type GetSystemRequest struct {
	ID uint `json:"id" validate:"required"`
}

// SystemResponse 系统响应结构
type SystemResponse struct {
	ID          uint      `json:"id"`
	SystemID    string    `json:"systemID"`
	SystemName  string    `json:"systemName"`
	Description string    `json:"description"`
	KeyType     string    `json:"keyType"`
	PublicKey   string    `json:"publicKey"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// ConvertToSystemResponse 转换为响应格式
func ConvertToSystemResponse(system *systemModel.AuditSystemTable) *SystemResponse {
	return &SystemResponse{
		ID:          system.ID,
		SystemID:    system.SystemID,
		SystemName:  system.SystemName,
		Description: system.Description,
		KeyType:     system.KeyType,
		PublicKey:   system.PublicKey,
		CreatedAt:   system.CreatedAt,
		UpdatedAt:   system.UpdatedAt,
	}
}

// ConvertToSystemResponseList 转换为响应列表格式
func ConvertToSystemResponseList(systems []*systemModel.AuditSystemTable) []*SystemResponse {
	result := make([]*SystemResponse, len(systems))
	for i, system := range systems {
		result[i] = ConvertToSystemResponse(system)
	}
	return result
}
